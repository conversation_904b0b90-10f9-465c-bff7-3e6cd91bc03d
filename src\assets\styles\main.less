// 导入基础样式
@import './variables.less';
@import './base.less';

// Hero区域样式
.hero {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      // filter: brightness(0.7);
    }
  }

  &-content {
    text-align: center;
    color: white;
    z-index: 1;

    h1 {
      color: #fff;
      font-size: 3.5rem;
      font-weight: @font-weight-bold;
      margin-bottom: @spacing-lg;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      animation: fadeInUp 1s ease;

      @media (max-width: @screen-sm) {
        font-size: 2.5rem;
      }
    }

    p {
      font-size: 1.3rem;
      margin-bottom: 40px;
      font-weight: @font-weight-light;
      line-height: 1.8;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      animation: fadeInUp 1s ease 0.2s both;
      color: white;

      @media (max-width: @screen-sm) {
        font-size: 1.1rem;
        margin-bottom: @spacing-xl;
      }
    }
  }

  .hero-buttons {
    display: flex;
    gap: @spacing-lg;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;

    @media (max-width: @screen-sm) {
      gap: @spacing-md;
    }
  }
}

.my-bullet-active {
  background: @primary-hover;
  opacity: 1;
}

.scroll-indicator {
  position: absolute;
  bottom: @spacing-xl;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: @font-size-xl;
  animation: bounce 2s infinite;
  cursor: pointer;
  transition: @transition;

  &:hover {
    color: @primary-light;
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }

  40% {
    transform: translateX(-50%) translateY(-10px);
  }

  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

// 通用区域样式
.section-header {
  text-align: center;
  margin-bottom: 60px;

  h2 {
    font-size: 36px;
    font-weight: @font-weight-bold;
    color: @text-color;
    margin-bottom: @spacing-md;
    position: relative;

    @media (max-width: @screen-sm) {
      font-size: 2rem;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: @primary-color;
      border-radius: 2px;
    }
  }

  p {
    font-size: 18px;
    color: #333;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: @screen-sm) {
      font-size: @font-size-base;
    }
  }
}

// 服务区域样式
.services {
  padding: 80px 0;
  background: @bg-light;

  @media (max-width: @screen-sm) {
    padding: 60px 0;
  }

  &-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: @spacing-xl;

    @media (max-width: @screen-sm) {
      grid-template-columns: 1fr;
      gap: @spacing-lg;
    }
  }
}

.service-item {
  background: white;
  padding: @spacing-xl;
  border-radius: @border-radius-large;
  text-align: center;
  box-shadow: @shadow;
  transition: @transition;

  &:hover {
    transform: translateY(-5px);
    box-shadow: @shadow-hover;
  }

  .service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, @primary-color, @primary-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto @spacing-lg;

    i {
      font-size: 32px;
      color: white;
    }
  }

  h3 {
    font-size: @font-size-lg;
    font-weight: @font-weight-bold;
    color: @text-color;
    margin-bottom: @spacing-md;
  }

  p {
    color: @text-light;
    line-height: 1.6;
  }
}

// 智能控制区域
.smart-control {
  padding: 66px 0;

  @media (max-width: @screen-sm) {
    padding: 60px 0;
  }

  .smart-content {
    position: relative;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    align-items: center;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: @shadow-hover;

    a {
      // position: absolute;
      // bottom: 30px;
      // left: 30px;

    }

    @media (max-width: @screen-md) {
      grid-template-columns: 1fr;
      gap: @spacing-xl;
      text-align: center;

      a {
        display: block;
      }
    }
  }

  .smart-text {
    text-align: left;
    padding: @spacing-xl;

    h2 {
      font-size: 28px;
      font-weight: @font-weight-bold;
      color: @text-color;
      margin-bottom: 0;

      @media (max-width: @screen-sm) {
        font-size: 1.6rem;
      }
    }

    p {
      font-size: 18px;
      color: #333;
      margin-bottom: @spacing-xl;
      line-height: 1.6;
    }
  }

  .smart-image {
    overflow: hidden;

    img {
      height: 500px;
      object-fit: cover;
      width: 100%;

      transition: @transition;

      &:hover {
        transform: scale(1.05);
        transition: @transition;
      }
    }
  }
}

.features-list {
  list-style: none;
  padding: 0;
  margin-bottom: @spacing-xl;

  li {
    display: flex;
    align-items: center;
    gap: @spacing-md;
    margin-bottom: @spacing-md;
    font-size: @font-size-base;
    color: @text-light;

    i {
      color: @primary-color;
      font-size: @font-size-sm;
    }
  }
}

// 产品预览区域
.products-preview {
  padding: 66px 0;
  background: @bg-light;

  @media (max-width: @screen-sm) {
    padding: 30px 0;
    padding-bottom: 60px;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: @spacing-xl;
    // margin-bottom: @spacing-xxl;

    @media (max-width: @screen-sm) {
      grid-template-columns: 1fr;
      gap: @spacing-lg;
    }
  }
}

.product-card {
  background: white;
  border-radius: @border-radius-large;
  overflow: hidden;
  box-shadow: @shadow;
  transition: @transition;

  &:hover {
    transform: translateY(-5px);
    box-shadow: @shadow-hover;
  }

  .product-image {
    height: 220px;
    overflow: hidden;
    width: 100%;

    img {
      width: 100%;
      height: 220px;
      object-fit: cover;
      transition: @transition;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  .product-info {
    padding: @spacing-lg;

    h3 {
      font-size: @font-size-lg;
      font-weight: @font-weight-bold;
      color: @text-color;
      margin-bottom: @spacing-sm;
    }

    p {
      color: @text-color;
      line-height: 1.6;
      margin: 0;
    }
  }
}

.view-details-btn {
  background: @primary-color;
  color: white;
  border: none;
  padding: @spacing-md @spacing-xl;
  border-radius: @border-radius;
  font-size: @font-size-base;
  font-weight: @font-weight-medium;
  cursor: pointer;
  transition: @transition;
  transform: translateY(20px);

  .product-card:hover & {
    transform: translateY(0);
  }

  .case-card:hover & {
    transform: translateY(0);
  }

  .product-grid-item:hover & {
    transform: translateY(0);
  }

  &:hover {
    background: @primary-hover;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// 移动端适配
@media (max-width: @screen-sm) {

  .view-details-btn {
    padding: @spacing-sm @spacing-lg;
    font-size: @font-size-sm;
  }

}

// 页面头部样式
.page-header {
  position: relative;
  height: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: @navbar-height;
  overflow: hidden;

  @media (max-width: @screen-sm) {
    height: auto;
    position: relative;
  }

  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    @media (max-width: @screen-sm) {
      position: relative;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      // filter: brightness(0.6);
    }
  }

  &-content {
    text-align: center;
    color: white;
    z-index: 2;

    h1 {
      color: #fff;
      font-size: 3rem;
      font-weight: @font-weight-bold;
      margin-bottom: @spacing-md;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);

      @media (max-width: @screen-sm) {
        font-size: 2.2rem;
      }
    }

    p {
      font-size: 1.2rem;
      margin-bottom: @spacing-lg;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      color: white;

      @media (max-width: @screen-sm) {
        font-size: 1rem;
      }
    }
  }
}

// 产品导航样式
.product-nav {
  padding: 40px 0;
  background: @bg-light;
  border-bottom: 1px solid @border-color;

  @media (max-width: @screen-sm) {
    padding: @spacing-lg 0;
  }
}

.product-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: @spacing-md;

  .category-btn {
    padding: 10px 20px;
    background: white;
    border: 2px solid @border-color;
    border-radius: 25px;
    color: @text-color;
    text-decoration: none;
    font-weight: @font-weight-medium;
    transition: @transition;

    &:hover,
    &.active {
      background: @primary-color;
      border-color: @primary-color;
      color: white;
      transform: translateY(-2px);
    }
  }
}