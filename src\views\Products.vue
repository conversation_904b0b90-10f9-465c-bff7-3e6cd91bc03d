<template>
  <div class="products-page">
    <!-- 页面头部 -->
    <PageHeader tag="产品中心" />

    <!-- 面包屑导航 -->
    <!-- <section class="breadcrumb-section">
      <div class="container">
        <nav class="breadcrumb">
          <router-link to="/">首页</router-link>
          <span class="separator">/</span>
          <router-link to="/products">产品中心</router-link>
          <span v-if="currentCategory" class="separator">/</span>
          <span v-if="currentCategory" class="current">{{ currentCategory.name }}</span>
        </nav>
      </div>
    </section> -->

    <!-- 产品展示 -->
    <section class="products-showcase">
      <div class="container">
        <SectionHeader pageKey="products" blockKey="product_showcase" />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 产品列表 -->
        <div v-else>
          <!-- 产品分类筛选 -->
          <div class="product-filters">
            <div class="filter-tabs">
              <button :class="['filter-tab', { active: activeCategory === null }]" @click="handleCategoryChange(null)">
                全部产品
              </button>
              <button v-for="category in topLevelCategories" :key="category.id"
                :class="['filter-tab', { active: activeCategory === category.id }]"
                @click="handleCategoryChange(category.id)">
                {{ category.name }}
              </button>
            </div>
          </div>

          <!-- 无产品状态 -->
          <div v-if="products.length === 0" class="no-products">
            <div class="no-products-content">
              <i class="fas fa-box-open"></i>
              <h3>暂无相关产品</h3>
              <p>该分类下暂时没有产品，请选择其他分类查看</p>
              <button class="btn btn-primary" @click="handleCategoryChange(null)">
                查看全部产品
              </button>
            </div>
          </div>

          <!-- 产品展示区域 -->
          <div v-else>
            <!-- 网格视图 -->
            <div class="products-grid">
              <ProductGridItem v-for="product in products" :key="product.id" :product="product"
                @view-detail="handleViewDetail" />
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper" v-if="pagination.pages > 1">
              <Pagination :current-page="pagination.page" :total-pages="pagination.pages"
                @page-change="handlePageChange" />
            </div>
          </div>
        </div>
      </div>
    </section>


  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import ProductGridItem from '@/components/product/ProductGridItem.vue'
import Pagination from '@/components/common/Pagination.vue'
import api from '@/api/index.js'
import { isMobile } from '@/utils/deviceDetect'

export default {
  name: 'Products',
  components: {
    PageHeader,
    SectionHeader,
    ProductGridItem,
    Pagination
  },
  data() {
    return {
      loading: false,
      activeCategory: null,
      categories: [],
      products: [],
      pagination: {
        page: 1,
        limit: 9,
        total: 0,
        pages: 0
      },

    }
  },
  computed: {
    currentCategory() {
      if (!this.activeCategory) return null
      return this.findCategoryById(this.activeCategory)
    },
    topLevelCategories() {
      // 只返回顶级分类（没有parent_id或parent_id为null的分类）
      return this.categories.filter(category => !category.parent_id)
    }
  },
  methods: {
    async loadCategories() {
      try {
        const response = await api.get('/api/front/products/categories')
        if (response.success) {
          this.categories = response.data.categories
        } else {
          console.error('获取分类失败:', response.message)
        }
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    },
    async loadProducts() {
      try {
        this.loading = true

        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        // 添加分类筛选
        if (this.activeCategory) {
          params.category_id = this.activeCategory
        }

        const response = await api.get('/api/front/products', { params })

        if (response.success) {
          this.products = response.data.products
          this.pagination = response.data.pagination
        } else {
          console.error('获取产品失败:', response.message)
        }
      } catch (error) {
        console.error('加载产品失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleCategoryChange(categoryId) {
      this.activeCategory = categoryId
      this.pagination.page = 1

      // 重新加载产品
      this.loadProducts()

      // 更新URL
      if (categoryId) {
        this.$router.push({
          path: '/products',
          query: { category: categoryId }
        })
      } else {
        this.$router.push({ path: '/products' })
      }
    },

    handlePageChange(page) {
      this.pagination.page = page
      this.loadProducts()
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },
    handleViewDetail(product) {
      // 跳转到产品详情页
      this.$router.push(`/products/${product.id}`)
    },
    findCategoryById(id, categories = this.categories) {
      // 简化的分类查找，只查找顶级分类
      return categories.find(category => category.id === id) || null
    },
    async handleUrlCategoryParam() {
      const categoryId = this.$route.query.category
      if (categoryId) {
        const parsedCategoryId = parseInt(categoryId)

        // 验证分类ID是否有效
        if (!isNaN(parsedCategoryId)) {
          // 检查分类是否存在于顶级分类中
          const categoryExists = this.topLevelCategories.find(cat => cat.id === parsedCategoryId)

          if (categoryExists) {
            // 设置激活的分类
            this.activeCategory = parsedCategoryId
            console.log('从URL参数激活分类:', categoryExists.name)
          } else {
            // 分类ID无效，显示警告并显示全部产品
            console.warn('无效的分类ID:', categoryId)
            this.activeCategory = null
            // 可选：移除无效的URL参数
            this.$router.replace({ path: '/products' })
          }
        } else {
          // 分类ID格式无效
          console.warn('分类ID格式无效:', categoryId)
          this.activeCategory = null
          this.$router.replace({ path: '/products' })
        }
      } else {
        // 没有分类参数，显示全部产品
        this.activeCategory = null
      }
    }
  },
  async mounted() {
    this.pagination.limit = isMobile() ? 20 : 9;
    // 先加载分类数据
    await this.loadCategories()

    // 检查URL参数并设置分类筛选
    await this.handleUrlCategoryParam()

    // 加载产品数据（会根据activeCategory进行筛选）
    await this.loadProducts()
  },
  watch: {
    // 监听路由变化，当URL参数改变时重新处理
    '$route'(to, from) {
      // 只有当路径相同但查询参数不同时才处理
      if (to.path === from.path && to.query.category !== from.query.category) {
        this.handleUrlCategoryParam()
        this.loadProducts()
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import url('@/assets/styles/variables.less');

.products-page {
  min-height: 100vh;

  .breadcrumb-section {
    background: #f8f9fa;
    padding: 15px 0;

    .breadcrumb {
      display: flex;
      align-items: center;
      font-size: 14px;

      a {
        color: #666;
        text-decoration: none;

        &:hover {
          color: #D80514;
        }
      }

      .separator {
        margin: 0 10px;
        color: #999;
      }

      .current {
        color: #D80514;
        font-weight: 500;
      }
    }
  }

  .products-showcase {
    padding: 66px 0;
    background: #f8f9fa;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
  }

  // 加载和错误状态
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .loading-spinner {
      text-align: center;

      i {
        font-size: 48px;
        color: #D80514;
        margin-bottom: 20px;
      }

      p {
        color: #666;
        margin: 0;
      }
    }
  }

  // 筛选器
  .product-filters {
    margin-bottom: 60px;

    .filter-tabs {
      display: flex;
      justify-content: center;
      gap: 16px;
      flex-wrap: wrap;
    }

    .filter-tab {
      padding: 12px 30px;
      border: 2px solid #ddd;
      background: white;
      color: #666;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
      font-size: 14px;

      &.active {
        border-color: @primary-color;
        color: @primary-color;
      }

      &:hover {
        border-color: @primary-color;
        color: @primary-color;
      }
    }
  }


  // 无产品状态
  .no-products {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .no-products-content {
      text-align: center;

      i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
      }

      h3 {
        color: #666;
        margin-bottom: 10px;
      }

      p {
        color: #999;
        margin-bottom: 20px;
      }

      .btn {
        padding: 10px 20px;
        background: #D80514;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          background: #b8040f;
        }
      }
    }
  }

  // 产品网格
  .products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 60px;

    // 平板端：2列
    @media (max-width: 1024px) and (min-width: 769px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 25px;
    }

    // 移动端：1列
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  // 分页
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }

  .product-advantages {
    padding: 80px 0;
    background: #f8f9fa;

    .advantages-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 30px;
    }

    .advantage-item {
      background: white;
      padding: 40px 30px;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .advantage-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #D80514, #ff1744);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;

        i {
          font-size: 32px;
          color: white;
        }
      }

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin: 0 0 15px 0;
      }

      p {
        color: #666;
        line-height: 1.6;
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .products-page {
    .container {
      padding: 0 15px;
    }

    .products-showcase {
      padding: 40px 0;
    }

    .product-filters {
      margin-bottom: 40px;

      .filter-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
        gap: 15px;
      }

      .filter-tab {
        flex-shrink: 0;
        padding: 10px 20px;
        font-size: 13px;
      }
    }

    .products-grid {
      grid-template-columns: 1fr;
      gap: 15px;
      margin-bottom: 40px;
    }

    .product-advantages {
      padding: 60px 0;

      .advantages-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .advantage-item {
        padding: 30px 20px;

        .advantage-icon {
          width: 60px;
          height: 60px;
          margin-bottom: 15px;

          i {
            font-size: 24px;
          }
        }

        h3 {
          font-size: 18px;
          margin-bottom: 10px;
        }

        p {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
