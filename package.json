{"name": "airfit-vue-website", "version": "1.0.0", "description": "雅克菲采暖官方网站 - Vue 2版本", "main": "src/main.js", "scripts": {"dev": "webpack-dev-server --mode development  --open", "build": "webpack --mode production", "build:analyze": "webpack --mode production --progress", "serve": "webpack-dev-server --mode development", "lint": "eslint --ext .js,.vue src"}, "keywords": ["vue", "airfit", "heating", "website"], "author": "Airfit Development Team", "license": "MIT", "dependencies": {"@playwright/test": "^1.54.1", "aos": "^2.3.4", "axios": "^1.10.0", "file-saver": "^2.0.5", "vue": "^2.6.14", "vue-awesome-swiper": "^4.1.1", "vue-count-to": "^1.0.13", "vue-lazyload": "^1.3.3", "vue-router": "^3.5.4"}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.17.0", "babel-loader": "^8.2.3", "copy-webpack-plugin": "^10.2.4", "css-loader": "^6.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "less": "^4.1.2", "less-loader": "^10.2.0", "style-loader": "^3.3.1", "url-loader": "^4.1.1", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14", "webpack": "^5.69.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}