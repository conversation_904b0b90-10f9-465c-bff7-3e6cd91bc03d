<template>
  <div class="heating-knowledge">
    <!-- 页面头部 -->
    <PageHeader tag="采暖知识" />

    <!-- 知识展示 -->
    <section class="knowledge-showcase">
      <div class="container">
        <SectionHeader pageKey="knowledge" blockKey="knowledge_showcase" />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchKnowledge">重试</button>
          </div>
        </div>

        <!-- 知识列表 -->
        <div v-else>
          <!-- 无知识状态 -->
          <div v-if="knowledge.length === 0" class="no-knowledge">
            <div class="no-knowledge-content">
              <i class="fas fa-book-open"></i>
              <h3>暂无采暖知识</h3>
              <p>目前还没有发布采暖知识，请稍后再来查看</p>
            </div>
          </div>

          <!-- 知识列表 -->
          <div v-else class="knowledge-grid">
            <div v-for="(item, index) in knowledge" :key="item.id" @click="viewKnowledge(item)" class="knowledge-card"
              data-aos="fade-up" :data-aos-delay="index * 100">
              <div class="knowledge-image">
                <img v-if="item.thumbnail" :src="item.thumbnail" :alt="item.title">
                <div v-else class="no-image">
                  <i class="fas fa-book-open"></i>
                  <p>暂无图片</p>
                </div>
                <div class="knowledge-overlay">
                  <div class="overlay-content">
                    <button class="view-details-btn">
                      查看详情
                    </button>
                  </div>
                </div>
              </div>

              <div class="knowledge-content">
                <h3 class="knowledge-title">{{ item.title }}</h3>
                <p class="knowledge-summary">{{ item.summary }}</p>

                <div class="knowledge-meta">
                  <div class="knowledge-tags" v-if="item.tags && item.tags.length > 0">
                    <span v-for="tag in item.tags.slice(0, 3)" :key="tag" class="tag">
                      {{ tag }}
                    </span>
                  </div>
                  <div class="knowledge-date">
                    <i class="fas fa-calendar-alt"></i>
                    <span>{{ formatDate(item.created_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pagination.pages > 1" class="pagination-wrapper">
            <Pagination :current-page="pagination.page" :total-pages="pagination.pages" :total-items="pagination.total"
              @page-change="handlePageChange" />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import Pagination from '@/components/common/Pagination.vue'
import api from '@/api'
import { formatDateTime } from '@/utils/dateFormat'
import { isMobile } from '@/utils/deviceDetect'

export default {
  name: 'HeatingKnowledge',
  components: {
    PageHeader,
    SectionHeader,
    Pagination
  },
  data() {
    return {
      knowledge: [],
      pagination: {
        page: 1,
        limit: 9,
        total: 0,
        pages: 0
      },
      loading: false,
      error: null
    }
  },
  mounted() {
    this.pagination.limit = isMobile() ? 20 : 9;
    this.fetchKnowledge()
  },
  methods: {
    async fetchKnowledge() {
      this.loading = true
      this.error = null

      try {
        const response = await api.get('/api/front/heating-knowledge', {
          params: {
            page: this.pagination.page,
            limit: this.pagination.limit
          }
        })

        if (response.success) {
          this.knowledge = response.data.knowledge
          this.pagination = response.data.pagination
        } else {
          this.error = response.data.message || '获取采暖知识失败'
        }
      } catch (error) {
        console.error('获取采暖知识失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    handlePageChange(page) {
      this.pagination.page = page
      this.fetchKnowledge()
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },

    viewKnowledge(item) {
      // 新开页签
      window.open(`/heating-knowledge/${item.id}`)
    },

    formatDate(dateString) {
      return formatDateTime(dateString, 'YYYY-MM-DD')
    },
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.heating-knowledge {
  .knowledge-showcase {
    padding: 66px 0;
    background-color: #f8f9fa;

    @media (max-width: @screen-sm) {
      padding: 60px 0;
    }
  }

  .knowledge-grid {
    display: grid;
    grid-template-columns: 1fr; // 移动端优先，默认单列
    gap: 20px;
    margin-bottom: 40px;

    // 平板及以上设备显示多列
    @media (min-width: 769px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }

    // 桌面端显示三列
    @media (min-width: 1025px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 30px;
    }
  }

  .no-knowledge {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .no-knowledge-content {
      text-align: center;

      i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
      }

      h3 {
        color: #666;
        margin-bottom: 10px;
        font-size: 24px;
      }

      p {
        color: #999;
        margin-bottom: 30px;
        font-size: 16px;
      }
    }
  }

  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .loading-spinner,
    .error-content {
      text-align: center;

      i {
        font-size: 48px;
        color: @text-lighter;
        margin-bottom: 16px;
      }

      h3 {
        margin: 16px 0 8px;
        color: @text-color;
      }

      p {
        color: @text-light;
        margin-bottom: 16px;
      }

      .btn {
        background: @primary-color;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: @border-radius-small;
        cursor: pointer;
        transition: @transition;

        &:hover {
          background: @primary-hover;
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }
}

.knowledge-card {
  background: white;
  border-radius: 25px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;



  .knowledge-image {
    position: relative;
    height: 220px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
      background: #f1f1f1;
    }

    .no-image {
      width: 100%;
      height: 100%;
      background: @bg-light;
      display: none;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: @text-lighter;

      i {
        font-size: 48px;
        margin-bottom: 12px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    .knowledge-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .overlay-content {
        text-align: center;
        color: white;
        padding: @spacing-lg;

        h3 {
          color: #fff;
          font-size: 18px;
          font-weight: @font-weight-bold;
          margin-bottom: @spacing-md;
          line-height: 1.3;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .knowledge-details {
          margin-bottom: @spacing-lg;

          .detail-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: @spacing-xs;
            font-size: 14px;

            i {
              margin-right: @spacing-xs;
              width: 16px;
            }
          }
        }

        .btn {
          background: white;
          color: @primary-color;
          border: 2px solid white;
          padding: 8px 20px;
          border-radius: 20px;
          font-weight: @font-weight-medium;
          transition: @transition;

          &:hover {
            background: transparent;
            color: white;
          }
        }
      }
    }

    &:hover {
      .knowledge-overlay {
        opacity: 1;
      }
    }
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);

    .knowledge-image img {
      transform: scale(1.05);
    }
  }


  .knowledge-content {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .knowledge-title {
      font-size: 18px;
      font-weight: 600;
      color: @text-color;
      margin-bottom: 12px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .knowledge-summary {
      color: @text-light;
      line-height: 1.6;
      margin-bottom: 16px;
      flex: 1;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .knowledge-meta {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 12px;

      .knowledge-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .tag {
          padding: 4px 8px;
          background: #f0f0f0;
          border-radius: 12px;
          font-size: 11px;
          color: #666;
        }
      }

      .knowledge-date {
        display: flex;
        align-items: center;
        justify-content: right;
        gap: 6px;
        color: @text-lighter;
        font-size: 14px;
      }
    }
  }

}

@media (max-width: 768px) {
  .knowledge-card {
    .knowledge-content {
      padding: 20px;

      .knowledge-title {
        font-size: 16px;
      }

      .knowledge-summary {
        font-size: 14px;
      }
    }
  }
}
</style>
