<template>
  <div class="video-tutorials">
    <!-- 页面头部 -->
    <PageHeader tag="视频教程" />

    <!-- 视频展示 -->
    <section class="video-showcase">
      <div class="container">
        <SectionHeader pageKey="video" blockKey="video_showcase" />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchVideos">重试</button>
          </div>
        </div>

        <!-- 视频列表 -->
        <div v-else>
          <!-- 视频类型筛选 -->
          <div class="video-filters">
            <div class="filter-tabs">
              <button v-for="videoType in videoTypes" :key="videoType.key"
                :class="['filter-tab', { active: activeFilter === videoType.key }]"
                @click="setActiveFilter(videoType.key)">
                {{ videoType.name }}
              </button>
            </div>
          </div>

          <!-- 无视频状态 -->
          <div v-if="videos.length === 0" class="no-videos">
            <div class="no-videos-content">
              <i class="fas fa-video"></i>
              <h3>暂无相关视频</h3>
              <p>该分类下暂时没有视频教程，请选择其他分类查看</p>
              <button class="btn btn-primary" @click="setActiveFilter('all')">
                查看全部教程
              </button>
            </div>
          </div>

          <!-- 视频列表 -->
          <div v-else class="videos-grid">
            <div v-for="(video, index) in videos" :key="video.id" class="video-card" data-aos="fade-up"
              :data-aos-delay="index * 100">
              <div class="video-player" v-html="video.video_code"></div>
              <div class="video-content">
                <div class="video-type">
                  <span class="type-badge" :style="getVideoTypeStyle(video.type)">
                    {{ getVideoTypeName(video.type) }}
                  </span>
                </div>
                <h3 class="video-title">{{ video.title }}</h3>
                <p class="video-summary">{{ video.summary }}</p>
                <div class="video-meta">
                  <div class="video-tags" v-if="video.tags && video.tags.length > 0">
                    <span v-for="tag in video.tags" :key="tag" class="tag">
                      {{ tag }}
                    </span>
                  </div>
                  <div class="video-date">
                    <i class="fas fa-calendar-alt"></i>
                    {{ formatDate(video.created_at) }}
                  </div>
                </div>

              </div>
            </div>
          </div>

          <!-- 分页 -->
          <Pagination v-if="pagination.pages > 1" :current-page="pagination.page" :total-pages="pagination.pages"
            :total-items="pagination.total" @page-change="handlePageChange" />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import Pagination from '@/components/common/Pagination.vue'
import api, { API } from '@/api/index.js'
import { isMobile } from '@/utils/deviceDetect'
import { formatDateTime } from '@/utils/dateFormat'

export default {
  name: 'VideoTutorials',
  components: {
    PageHeader,
    SectionHeader,
    Pagination
  },
  data() {
    return {
      videos: [],
      videoTypes: [], // 动态视频类型
      loading: false,
      error: null,
      activeFilter: 'all',
      pagination: {
        page: 1,
        limit: 9,
        total: 0,
        pages: 0
      }
    }
  },
  mounted() {
    this.pagination.limit = isMobile() ? 20 : 9;
    this.fetchVideoTypes()
    this.fetchVideos()
  },
  methods: {
    formatDate(dateString) {
      return formatDateTime(dateString, 'YYYY-MM-DD')
    },
    // 获取视频类型配置
    async fetchVideoTypes() {
      try {
        const response = await API.getSystemConfig('video_types')
        if (response.success) {
          // 添加"全部教程"选项
          this.videoTypes = [
            { key: 'all', name: '全部教程' },
            ...(response.data || [])
          ]
        }
      } catch (error) {
        console.error('获取视频类型配置失败:', error)
      }
    },

    async fetchVideos() {
      try {
        this.loading = true
        this.error = null

        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        // 添加筛选参数
        if (this.activeFilter !== 'all') {
          params.type = this.activeFilter
        }

        const response = await api.get('/api/front/video-tutorials', { params })

        if (response.success) {
          this.videos = response.data.videos
          this.pagination = response.data.pagination
        } else {
          throw new Error(response.message || '获取数据失败')
        }

      } catch (error) {
        console.error('获取视频列表失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    setActiveFilter(filter) {
      this.activeFilter = filter
      this.pagination.page = 1
      this.fetchVideos()
    },

    handlePageChange(page) {
      this.pagination.page = page
      this.fetchVideos()
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },

    // 获取视频类型中文名称
    getVideoTypeName(typeKey) {
      const videoType = this.videoTypes.find(type => type.key === typeKey);
      return videoType ? videoType.name : typeKey;
    },

    // 根据视频类型key从颜色池获取样式
    getVideoTypeStyle(typeKey) {
      // 视频类型颜色池（10个精心挑选的颜色）
      const colorPool = [
        '#1890ff', // 蓝色 - 适合安装教程
        '#52c41a', // 绿色 - 适合操作指南
        '#fa541c', // 橙色 - 适合维护保养
        '#722ed1', // 紫色 - 适合故障排除
        '#eb2f96', // 粉色 - 适合使用教程
        '#13c2c2', // 青色
        '#faad14', // 金色
        '#f5222d', // 红色
        '#2f54eb', // 深蓝
        '#a0d911'  // 浅绿
      ];

      // 根据key生成稳定的索引
      const index = this.videoTypes.findIndex(type => type.key === typeKey);

      return {
        backgroundColor: colorPool[index],
        color: '#fff',
        border: 'none'
      };
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.video-tutorials {
  min-height: 100vh;
}

.video-showcase {
  padding: 66px 0;
  background: @bg-light;
}

.container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @spacing-lg;
}

// 加载和错误状态
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loading-spinner,
.error-content {
  text-align: center;

  i {
    font-size: 48px;
    color: @primary-color;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
    margin-bottom: @spacing-lg;
  }
}

// 筛选器
.video-filters {
  margin-bottom: @spacing-xxl;
}

.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 12px 30px;
  border: 2px solid @border-color;
  background: @bg-color;
  color: @text-light;
  border-radius: 50px;
  cursor: pointer;
  transition: @transition;
  font-weight: @font-weight-medium;

  &.active {
    border-color: @primary-color;
    color: @primary-color;
  }

  &:hover {
    border-color: @primary-color;
    color: @primary-color;
  }
}

// 无视频状态
.no-videos {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.no-videos-content {
  text-align: center;

  i {
    font-size: 64px;
    color: @text-lighter;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
    margin-bottom: @spacing-lg;
  }
}

// 视频网格
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: @spacing-xl;
  margin-bottom: @spacing-xxl;
}

// 视频卡片
.video-card {
  background: @bg-color;
  border-radius: @border-radius-large;
  box-shadow: @shadow;
  overflow: hidden;
  transition: @transition;

  &:hover {
    box-shadow: @shadow-hover;
    transform: translateY(-5px);
  }
}

.video-player {
  position: relative;
  width: 100%;
  height: 250px;
  background: #000;

  :deep(iframe) {
    width: 100%;
    height: 100%;
    border: none;
  }
}

.video-content {
  padding: @spacing-lg;
}

.video-type {
  margin-bottom: @spacing-md;
}

.type-badge {
  display: inline-block;
  padding: @spacing-xs @spacing-md;
  border-radius: @border-radius-small;
  font-size: @font-size-xs;
  font-weight: @font-weight-medium;

  &.install {
    background: #e3f2fd;
    color: #1976d2;
  }

  &.usage {
    background: #f3e5f5;
    color: #7b1fa2;
  }
}

.video-title {
  font-size: @font-size-lg;
  font-weight: @font-weight-medium;
  color: @text-color;
  margin-bottom: @spacing-sm;
  line-height: 1.4;
}

.video-summary {
  color: @text-light;
  line-height: 1.6;
  margin-bottom: @spacing-md;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
}

.video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: @spacing-xs;
}

.tag {
  display: inline-block;
  padding: @spacing-xs @spacing-sm;
  background: @bg-light;
  color: @text-lighter;
  border-radius: @border-radius-small;
  font-size: @font-size-xs;
}

.video-date {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: 6px;
  color: @text-lighter;
  font-size: 14px;
}

// 响应式设计
@media (max-width: @screen-md) {
  .videos-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: @spacing-lg;
  }

  .filter-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: @spacing-sm;
  }

  .filter-tab {
    flex-shrink: 0;
    padding: @spacing-sm @spacing-lg;
  }
}

@media (max-width: @screen-sm) {
  .container {
    padding: 0 @spacing-md;
  }

  .video-showcase {
    padding: @spacing-xl 0;
  }

  .videos-grid {
    grid-template-columns: 1fr;
  }

  .video-player {
    height: 200px;
  }

  .video-content {
    padding: @spacing-md;
  }
}
</style>
