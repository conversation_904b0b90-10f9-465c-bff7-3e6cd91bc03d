<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>经销体系管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加经销商
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="公司名称">
            <el-input v-model="searchForm.company_name" placeholder="请输入公司名称" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="所属省份">
            <el-select v-model="searchForm.province" placeholder="请选择省份" clearable style="width: 150px;"
              @change="handleSearchProvinceChange">
              <el-option v-for="province in provinces" :key="province.value" :label="province.label"
                :value="province.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属城市">
            <el-select v-model="searchForm.city_name" placeholder="请选择城市" clearable style="width: 150px;"
              :disabled="!searchForm.province">
              <el-option v-for="city in searchCities" :key="city.value" :label="city.label" :value="city.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 经销商表格 -->
      <el-table v-loading="loading" :data="dealerList" border class="dealer-table">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="province" label="所属省份" width="120" />
        <el-table-column prop="city_name" label="所属城市" width="120" />

        <el-table-column prop="company_name" label="公司名称" min-width="200" />

        <el-table-column prop="company_location" label="公司位置" min-width="200" show-overflow-tooltip />

        <el-table-column prop="phone" label="联系电话" width="140" />



        <el-table-column prop="sort_order" label="排序" width="80" align="center" />

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          :total="pagination.total" layout="total, sizes, prev, pager, next, jumper" />
      </div>
    </div>

    <!-- 添加/编辑经销商对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
      <el-form ref="dealerForm" :model="dealerForm" :rules="dealerRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属省份" prop="province">
              <el-select v-model="dealerForm.province" placeholder="请选择省份" style="width: 100%;"
                @change="handleFormProvinceChange">
                <el-option v-for="province in provinces" :key="province.value" :label="province.label"
                  :value="province.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属城市" prop="city_name">
              <el-select v-model="dealerForm.city_name" placeholder="请选择城市" style="width: 100%;"
                :disabled="!dealerForm.province">
                <el-option v-for="city in formCities" :key="city.value" :label="city.label" :value="city.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- <el-col :span="12">
            <el-form-item label="城市标识" prop="city">
              <el-input v-model="dealerForm.city" placeholder="请输入城市标识（如：北京丰台区）" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="dealerForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司名称" prop="company_name">
              <el-input v-model="dealerForm.company_name" placeholder="请输入公司名称" />
            </el-form-item>
          </el-col>
        </el-row>



        <el-form-item label="公司位置" prop="company_location">
          <el-input v-model="dealerForm.company_location" type="textarea" :rows="3" placeholder="请输入公司详细地址" />
        </el-form-item>



        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="dealerForm.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序序号">
              <el-input-number v-model="dealerForm.sort_order" :min="0" :max="9999" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { dealers } from '@/api/dealers'
import { getProvinceList, getCityListByProvince } from '@/utils/area'

export default {
  name: 'DealerManagement',
  data() {
    return {
      loading: false,
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      editId: null,
      dealerList: [],
      searchForm: {
        company_name: '',
        province: '',
        city_name: '',
        status: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      dealerForm: {
        province: '',
        city_name: '',
        company_name: '',
        company_location: '',
        phone: '',
        status: 1,
        sort_order: 0
      },
      dealerRules: {
        province: [
          { required: true, message: '请选择所属省份', trigger: 'change' }
        ],
        city_name: [
          { required: true, message: '请选择所属城市', trigger: 'change' }
        ],
        company_name: [
          { required: true, message: '请输入公司名称', trigger: 'blur' },
          { min: 2, max: 200, message: '公司名称长度在 2 到 200 个字符', trigger: 'blur' }
        ]
      },
      // 省份列表
      provinces: [],
      // 搜索用的城市列表
      searchCities: [],
      // 表单用的城市列表
      formCities: []
    }
  },

  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑经销商' : '添加经销商'
    }
  },

  mounted() {
    this.loadProvinces()
    this.loadDealerList()
  },

  methods: {
    // 加载经销商列表
    async loadDealerList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.current,
          limit: this.pagination.size,
          company_name: this.searchForm.company_name,
          province: this.searchForm.province,
          city: this.searchForm.city_name,
          status: this.searchForm.status
        }

        const response = await dealers.getList(params)
        if (response.success) {
          this.dealerList = response.data.dealers
          this.pagination.total = response.data.pagination.total_items
        }
      } catch (error) {
        console.error('加载经销商列表失败:', error)
        this.$message.error('加载经销商列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载省份数据
    loadProvinces() {
      this.provinces = getProvinceList()
    },

    // 搜索表单省份变化处理
    handleSearchProvinceChange() {
      this.searchForm.city_name = '' // 重置城市选择
      this.searchCities = []

      if (this.searchForm.province) {
        this.searchCities = getCityListByProvince(this.searchForm.province)
      }
    },

    // 表单省份变化处理
    handleFormProvinceChange() {
      this.dealerForm.city_name = '' // 重置城市选择
      this.formCities = []

      if (this.dealerForm.province) {
        this.formCities = getCityListByProvince(this.dealerForm.province)
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadDealerList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        company_name: '',
        city_name: '',
        covered_province: '',
        status: ''
      }
      this.pagination.current = 1
      this.loadDealerList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadDealerList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadDealerList()
    },

    // 添加经销商
    handleAdd() {
      this.isEdit = false
      this.editId = null
      this.dialogVisible = true
    },

    // 编辑经销商
    handleEdit(row) {
      this.isEdit = true
      this.editId = row.id
      this.dealerForm = {
        province: row.province || '',
        city_name: row.city_name || '',
        company_name: row.company_name,
        company_location: row.company_location || '',
        phone: row.phone || '',
        status: row.status,
        sort_order: row.sort_order
      }

      // 如果有省份，加载对应的城市列表
      if (this.dealerForm.province) {
        this.formCities = getCityListByProvince(this.dealerForm.province)
      }

      this.dialogVisible = true
    },

    // 删除经销商
    handleDelete(row) {
      this.$confirm(`确定要删除经销商"${row.company_name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await dealers.delete(row.id)
          if (response.success) {
            this.$message.success('删除成功')
            this.loadDealerList()
          }
        } catch (error) {
          console.error('删除经销商失败:', error)
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 状态切换
    async handleStatusChange(row) {
      try {
        const response = await dealers.update(row.id, { status: row.status })
        if (response.success) {
          this.$message.success('状态更新成功')
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dealerForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const formData = { ...this.dealerForm }

            let response
            if (this.isEdit) {
              response = await dealers.update(this.editId, formData)
            } else {
              response = await dealers.create(formData)
            }

            if (response.success) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功')
              this.dialogVisible = false
              this.loadDealerList()
            }
          } catch (error) {
            console.error('提交失败:', error)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 重置表单
    resetForm() {
      this.dealerForm = {
        province: '',
        city_name: '',
        company_name: '',
        company_location: '',
        phone: '',
        status: 1,
        sort_order: 0
      }
      this.formCities = [] // 重置城市列表
      if (this.$refs.dealerForm) {
        this.$refs.dealerForm.resetFields()
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

.search-bar {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.dealer-table {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: right;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 表格样式优化 */
.dealer-table .el-table__header {
  background-color: #f5f7fa;
}

.dealer-table .el-table__header th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

/* 标签样式 */
.el-tag {
  margin-right: 5px;
  margin-bottom: 2px;
}
</style>
