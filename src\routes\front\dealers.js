const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取经销商列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    city,
    province,
    keyword
  } = req.query;

  const offset = (page - 1) * limit;
  let whereConditions = ['status = 1']; // 只返回启用的经销商
  let params = [];

  // 省份筛选
  if (province) {
    whereConditions.push('province = ?');
    params.push(province);
  }

  // 城市筛选
  if (city) {
    whereConditions.push('(city_name = ? OR city = ?)');
    params.push(city, city);
  }

  // 关键词搜索
  if (keyword) {
    whereConditions.push('(company_name LIKE ? OR company_location LIKE ? OR covered_provinces LIKE ?)');
    params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
  }

  const whereClause = whereConditions.join(' AND ');

  // 查询经销商列表
  let sql = `
    SELECT
      id,
      province,
      city_name,
      city,
      company_name,
      company_location,
      phone,
      created_at
    FROM dealers
    WHERE ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const dealers = await query(sql, params);

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM dealers
    WHERE ${whereClause}
  `, params);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: '获取经销商列表成功',
    data: {
      dealers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取经销商详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 查询经销商详情
  const dealers = await query(`
    SELECT *
    FROM dealers
    WHERE id = ? AND status = 1
  `, [id]);

  if (dealers.length === 0) {
    return res.status(404).json({
      success: false,
      message: '经销商不存在或已停用'
    });
  }

  const dealer = dealers[0];

  res.json({
    success: true,
    message: '获取经销商详情成功',
    data: {
      dealer
    }
  });
}));

// 按城市分组获取经销商
router.get('/group/by-city', asyncHandler(async (req, res) => {
  const dealers = await query(`
    SELECT 
      city,
      COUNT(*) as count,
      GROUP_CONCAT(company_name ORDER BY sort_order ASC, created_at DESC) as companies
    FROM dealers
    WHERE status = 1
    GROUP BY city
    ORDER BY count DESC, city ASC
  `);

  // 处理companies字段，转换为数组
  const processedDealers = dealers.map(dealer => ({
    ...dealer,
    companies: dealer.companies ? dealer.companies.split(',') : []
  }));

  res.json({
    success: true,
    message: '获取城市分组经销商成功',
    data: {
      cities: processedDealers
    }
  });
}));

// 按省份分组获取经销商
router.get('/group/by-province', asyncHandler(async (req, res) => {
  // 获取所有经销商的覆盖省份信息
  const dealers = await query(`
    SELECT 
      id,
      city,
      company_name,
      covered_provinces
    FROM dealers
    WHERE status = 1 AND covered_provinces IS NOT NULL AND covered_provinces != ''
    ORDER BY sort_order ASC, created_at DESC
  `);

  // 处理省份分组
  const provinceMap = new Map();

  dealers.forEach(dealer => {
    if (dealer.covered_provinces) {
      let provinces = [];
      // 处理JSON格式的covered_provinces字段
      if (typeof dealer.covered_provinces === 'string') {
        try {
          provinces = JSON.parse(dealer.covered_provinces);
        } catch (e) {
          provinces = dealer.covered_provinces.split(',').map(p => p.trim());
        }
      } else if (Array.isArray(dealer.covered_provinces)) {
        provinces = dealer.covered_provinces;
      }

      provinces.forEach(province => {
        if (!provinceMap.has(province)) {
          provinceMap.set(province, []);
        }
        provinceMap.get(province).push({
          id: dealer.id,
          city: dealer.city,
          company_name: dealer.company_name
        });
      });
    }
  });

  // 转换为数组格式
  const provinceGroups = Array.from(provinceMap.entries()).map(([province, dealers]) => ({
    province,
    count: dealers.length,
    dealers
  })).sort((a, b) => b.count - a.count);

  res.json({
    success: true,
    message: '获取省份分组经销商成功',
    data: {
      provinces: provinceGroups
    }
  });
}));




module.exports = router;
