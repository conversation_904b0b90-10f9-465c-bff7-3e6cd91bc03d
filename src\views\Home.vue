<template>
  <div class="home">
    <!-- Hero Banner区域 -->
    <swiper class="swiper-container" :options="swiperOption" ref="swiper">
      <swiper-slide v-for="banner in bannerData" :key="banner.id">
        <div class="hero">
          <div class="hero-bg">
            <img :src="isMobile ? banner.mobile_image : banner.image" alt="现代家庭采暖">
          </div>
          <div class="hero-content">
            <div class="container">
              <h1>{{ banner.title }}</h1>
              <p v-html="banner.subtitle"></p>
            </div>
          </div>

        </div>
      </swiper-slide>
      <div class="swiper-pagination" slot="pagination"></div>
    </swiper>



    <!-- 智能控制 -->
    <section class="smart-control">
      <div class="container">
        <div v-if="brandPropositionLoading" class="loading-container">
          <div class="loading-spinner"></div>
        </div>
        <div v-else-if="brandProposition" class="smart-content">
          <div class="smart-text" data-aos="fade-right">
            <h2>{{ brandProposition.title }}</h2>
            <h3 v-if="brandProposition.subtitle">{{ brandProposition.subtitle }}</h3>
            <p>{{ brandProposition.description }}</p>
            <a href="#" class="btn btn-outline pc" @click.prevent="openDetailPage">
              了解详情 >
            </a>
          </div>

          <div class="smart-image" data-aos="fade-left">
            <img v-if="brandProposition.main_image" :src="brandProposition.main_image" :alt="brandProposition.title">
          </div>
        </div>
      </div>
      <a href="#" class="btn mobile" @click.prevent="openDetailPage">
        了解详情 >
      </a>
    </section>

    <!-- 案例展示 -->
    <section class="products-preview">
      <div class="container">
        <div class="section-header-with-action">
          <SectionHeader pageKey="home" blockKey="featured_cases" />

        </div>
        <div v-if="featuredProductsLoading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>
        <div v-else class="products-grid">
          <ProductCard v-for="(product, index) in featuredProducts" :key="product.id" :product="product"
            :delay="index * 100" @click="handleProductClick" @view-details="handleViewDetails" />
        </div>
        <router-link to="/cases" class="btn  more-btn">
          更多 >
        </router-link>
      </div>

    </section>


  </div>
</template>

<script>
import SectionHeader from '@/components/common/SectionHeader.vue'
import ProductCard from '@/components/common/ProductCard.vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import api from '@/api'
import { isMobile } from '@/utils/deviceDetect'

export default {
  name: 'Home',
  components: {
    SectionHeader,
    ProductCard,
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      // 控制下载弹窗显示
      showDownloadModal: false,
      appDownloadUrl: '',
      swiperOption: {
        initialSlide: 5,
        loop: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          bulletActiveClass: 'my-bullet-active',
        },
        navigation: true,
        speed: 1000
      },
      bannerData: [],
      featuredProducts: [],
      featuredProductsLoading: false,
      // 品牌主张内容块
      brandProposition: null,
      brandPropositionLoading: false,
      isMobile: isMobile(),
    }
  },
  computed: {

  },
  methods: {
    // 获取首页banner数据
    getBannerData() {
      const bannerData = localStorage.getItem('bannerData')
      if (bannerData) {
        this.bannerData = JSON.parse(bannerData).filter(item => item.tags.includes('首页'))
      }
    },

    // 获取首页精选案例
    async getFeaturedCases() {
      try {
        this.featuredProductsLoading = true
        const response = await api.get('/api/front/energy-cases/homepage/featured', {
          params: { limit: 4 }
        })

        if (response.success) {
          // 将案例数据转换为产品卡片需要的格式
          this.featuredProducts = response.data.cases.map(caseItem => ({
            id: caseItem.id,
            name: caseItem.title,
            description: caseItem.summary,
            image: caseItem.main_image,
            case_type: caseItem.case_type,
            case_area: caseItem.case_area,
            product_category: caseItem.product_category
          }))
        }
      } catch (error) {
        console.error('获取首页精选案例失败:', error)
      } finally {
        this.featuredProductsLoading = false
      }
    },

    // 获取品牌主张内容块
    async getBrandProposition() {
      try {
        this.brandPropositionLoading = true
        const response = await api.get('/api/front/page-content-blocks/home/<USER>')

        if (response.success) {
          this.brandProposition = response.data
        }
      } catch (error) {
        console.error('获取品牌主张内容失败:', error)
        this.brandProposition = null
      } finally {
        this.brandPropositionLoading = false
      }
    },

    handleDirectDownload() {
      console.log('用户点击了直接下载按钮')
      // 可以在这里添加下载统计等逻辑
    },
    handleProductClick(product) {
      console.log('案例卡片被点击:', product)
      // 跳转到案例详情页
      this.$router.push(`/cases/${product.id}`)
    },
    handleViewDetails(product) {
      console.log('查看详情按钮被点击:', product)
      // 跳转到案例详情页
      this.$router.push(`/cases/${product.id}`)
    },
    openDetailPage() {
      this.$router.push('/news/50')
    }
  },
  mounted() {
    this.getBannerData();
    this.getFeaturedCases();
    this.getBrandProposition();
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.hero-content {
  display: none;
}

// 样式已在main.less中定义，这里只添加特定的样式
.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease 0.4s both;

  .mr-1:hover {
    color: #fff;
    background-color: @primary-hover;

  }

  .mr-2:hover {
    color: #fff;
  }

  @media (max-width: 768px) {
    gap: 15px;
  }
}

// 带有右上角按钮的section header样式
.section-header-with-action {
  position: relative;
  margin-bottom: 60px;

  @media (max-width: 768px) {
    margin-bottom: 40px;
  }

  .more-btn {
    position: absolute;
    top: 0;
    right: 0;
    padding: 8px 20px;
    font-size: 14px;

    @media (max-width: @screen-sm) {
      position: static;
      margin-top: 20px;
      display: block;
      width: fit-content;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

// 首页精选案例特定样式 - 确保PC端4个案例在一行
.products-preview {
  .container {
    position: relative;

    .more-btn {
      position: absolute;
      top: 95px;
      right: 0;
    }

    @media (max-width: @screen-sm) {
      .more-btn {
        position: absolute;
        bottom: -55px;
        left: 50%;
        transform: translateX(-50%);
        top: auto;
      }
    }
  }




  .products-grid {
    @media (min-width: 1200px) {
      grid-template-columns: repeat(4, 1fr);
      gap: @spacing-lg;
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      grid-template-columns: repeat(4, 1fr);
      gap: @spacing-md;
    }

    @media (min-width: 768px) and (max-width: 991px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      text-align: center;
      color: @text-light;

      i {
        font-size: 2rem;
        margin-bottom: 10px;
        color: @primary-color;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

// APP功能特色样式
.app-features {
  text-align: left;

  h5 {
    margin: 0 0 @spacing-md 0;
    font-size: @font-size-base;
    font-weight: @font-weight-medium;
    color: @text-color;
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      display: flex;
      align-items: center;
      gap: @spacing-sm;
      padding: @spacing-xs 0;
      color: @text-light;
      font-size: @font-size-sm;

      i {
        color: @primary-color;
        font-size: 12px;
      }
    }
  }
}

// APP功能特色样式
.app-features {
  text-align: left;

  h5 {
    margin: 0 0 @spacing-md 0;
    font-size: @font-size-base;
    font-weight: @font-weight-medium;
    color: @text-color;
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      display: flex;
      align-items: center;
      gap: @spacing-sm;
      padding: @spacing-xs 0;
      color: @text-light;
      font-size: @font-size-sm;

      i {
        color: @primary-color;
        font-size: 12px;
      }
    }
  }
}

// APP功能特色样式
.app-features {
  text-align: left;

  h5 {
    margin: 0 0 @spacing-md 0;
    font-size: @font-size-base;
    font-weight: @font-weight-medium;
    color: @text-color;
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      display: flex;
      align-items: center;
      gap: @spacing-sm;
      padding: @spacing-xs 0;
      color: @text-light;
      font-size: @font-size-sm;

      i {
        color: @primary-color;
        font-size: 12px;
      }
    }
  }
}

// 智能控制部分样式优化
.smart-control {
  position: relative;

  .smart-text {
    h3 {
      color: @primary-color;
      font-size: 16px;
      margin: 10px 0 20px 0;
      font-weight: @font-weight-medium;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid @primary-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  a.mobile {
    display: none;
  }

  @media (max-width: @screen-sm) {
    .smart-content {
      gap: 0;

      a.pc {
        display: none;
      }

      .smart-image {
        img {
          height: auto;
        }
      }
    }

    .smart-text p {
      margin-bottom: 0;
    }



    a.mobile {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 7px;
      display: block;
    }
  }

}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
