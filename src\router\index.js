import Vue from 'vue'
import VueRouter from 'vue-router'

// 只保留首页直接导入
import Home from '../views/Home.vue'

// 其他页面组件使用懒加载
const Products = () => import('../views/Products.vue')
const HeatingKnowledge = () => import('../views/HeatingKnowledge.vue')
const HeatingKnowledgeDetail = () => import('../views/HeatingKnowledgeDetail.vue')
const Cases = () => import('../views/Cases.vue')
const Stores = () => import('../views/Stores.vue')
const CompanyProfile = () => import('../views/About.vue')
const ContactUs = () => import('../views/ContactUs.vue')

// 详情页面组件
const ProductDetail = () => import('../views/ProductDetail.vue')
const CaseDetail = () => import('../views/CaseDetail.vue')
const NewsDetail = () => import('../views/NewsDetail.vue')

// 资料库和视频教程页面
const ResourceLibrary = () => import('../views/ResourceLibrary.vue')
const VideoTutorials = () => import('../views/VideoTutorials.vue')

// 新闻页面
const News = () => import('../views/News.vue')

// 发展历程页面
const DevelopmentHistory = () => import('../views/DevelopmentHistory.vue')

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页 - 雅克菲采暖'
    }
  },
  {
    path: '/products',
    name: 'Products',
    component: Products,
    meta: {
      title: '产品中心 - 雅克菲采暖'
    }
  },
  {
    path: '/products/:id',
    name: 'ProductDetail',
    component: ProductDetail,
    meta: {
      title: '产品详情 - 雅克菲采暖'
    }
  },

  {
    path: '/heating-knowledge/:id',
    name: 'HeatingKnowledgeDetail',
    component: HeatingKnowledgeDetail,
    meta: {
      title: '知识详情 - 雅克菲采暖'
    }
  },
  {
    path: '/cases',
    name: 'Cases',
    component: Cases,
    meta: {
      title: '工程案例 - 雅克菲采暖'
    }
  },
  {
    path: '/cases/:id',
    name: 'CaseDetail',
    component: CaseDetail,
    meta: {
      title: '案例详情 - 雅克菲采暖'
    }
  },
  {
    path: '/stores',
    name: 'Stores',
    component: Stores,
    meta: {
      title: '门店查询 - 雅克菲采暖'
    }
  },
  {
    path: '/news',
    name: 'News',
    component: News,
    meta: {
      title: '新闻动态 - 雅克菲采暖'
    }
  },
  {
    path: '/news/:id',
    name: 'NewsDetail',
    component: NewsDetail,
    meta: {
      title: '新闻详情 - 雅克菲采暖'
    }
  },
  {
    path: '/about',
    name: 'About',
    redirect: '/company-profile',
    meta: {
      title: '关于我们 - 雅克菲采暖'
    }
  },
  {
    path: '/company-profile',
    name: 'CompanyProfile',
    component: CompanyProfile,
    meta: {
      title: '公司简介 - 雅克菲采暖'
    }
  },
  {
    path: '/development-history',
    name: 'DevelopmentHistory',
    component: DevelopmentHistory,
    meta: {
      title: '发展历程 - 雅克菲采暖'
    }
  },
  {
    path: '/contact-us',
    name: 'ContactUs',
    component: ContactUs,
    meta: {
      title: '联系我们 - 雅克菲采暖'
    }
  },
  {
    path: '/academy',
    name: 'Academy',
    redirect: '/resource-library',
    meta: {
      title: '服务支持 - 雅克菲采暖'
    }
  },
  {
    path: '/resource-library',
    name: 'ResourceLibrary',
    component: ResourceLibrary,
    meta: {
      title: '资料库 - 雅克菲采暖'
    }
  },
  {
    path: '/video-tutorials',
    name: 'VideoTutorials',
    component: VideoTutorials,
    meta: {
      title: '视频教程 - 雅克菲采暖'
    }
  },
  {
    path: '/heating-knowledge',
    name: 'HeatingKnowledge',
    component: HeatingKnowledge,
    meta: {
      title: '采暖知识 - 雅克菲采暖'
    }
  },
  {
    path: '*',
    redirect: '/'
  }
]

const router = new VueRouter({
  mode: 'history',
  base: '/',
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

// 解决Vue Router重复导航错误
const originalPush = VueRouter.prototype.push
const originalReplace = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
