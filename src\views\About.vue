<template>
  <div class="about">
    <!-- 页面头部 -->
    <PageHeader tag="公司简介" />

    <!-- 公司简介 -->
    <section class="company-intro">
      <div class="container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchCompanyProfile">重试</button>
          </div>
        </div>

        <!-- 公司简介内容 -->
        <div v-else class="intro-content">
          <div class="intro-text" data-aos="fade-up">
            <div class="company-summary" v-if="companyProfile.summary">
              <h2>公司简介</h2>
              <p class="summary-text">{{ companyProfile.summary }}</p>
            </div>
            <!-- <div class="intro-image" data-aos="fade-up">
              <img v-if="companyProfile.main_image" :src="companyProfile.main_image" alt="公司介绍">
            </div> -->
            <div class="company-content" v-if="companyProfile.content">
              <div v-html="companyProfile.content" class="rich-content"></div>
            </div>
          </div>

        </div>
      </div>
    </section>

    <!-- 企业文化 -->
    <section class="company-culture">
      <div class="container">
        <SectionHeader pageKey="about" blockKey="company_culture" />

        <!-- 企业文化内容 -->
        <div v-if="!loading && !error && companyProfile.culture" class="culture-content">
          <div v-html="companyProfile.culture" class="rich-content"></div>
        </div>

        <!-- 如果没有文化内容，显示默认内容 -->
        <div v-else-if="!loading && !error" class="default-culture">
          <p>我们正在完善企业文化内容，敬请期待...</p>
        </div>
      </div>
    </section>


  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import CountTo from 'vue-count-to'
import api from '@/api'

export default {
  name: 'About',
  components: {
    PageHeader,
    SectionHeader,
    CountTo
  },
  data() {
    return {
      loading: true,
      error: null,
      companyProfile: {
        main_image: '',
        summary: '',
        content: '',
        culture: ''
      }
    }
  },
  async mounted() {
    await this.fetchCompanyProfile()
  },
  methods: {
    async fetchCompanyProfile() {
      this.loading = true
      this.error = null

      try {
        const response = await api.get('/api/front/about-us/profile')

        if (response.success) {
          this.companyProfile = response.data.profile || {}
        } else {
          throw new Error(response.message || '获取公司简介失败')
        }
      } catch (error) {
        console.error('获取公司简介失败:', error)
        this.error = error.response?.message || error.message || '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.company-intro {
  padding: 66px 0;
  background: #f8f9fa;


  .intro-text {
    .company-summary {
      margin-bottom: 30px;


      h2 {
        text-align: center;
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;

        @media (max-width: 768px) {
          font-size: 1.8rem;
        }
      }

      .summary-text {
        color: #666;
        line-height: 1.8;
        margin-bottom: 20px;
        font-size: 16px;
        background: #fff;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #D80514;
        box-shadow: 0 1px 7px rgba(0, 0, 0, 0.05);
      }
    }

    .company-content {
      .rich-content {
        color: #666;
        line-height: 1.8;
        font-size: 16px;

        h3 {
          color: #D80514;
          font-size: 1.5rem;
          font-weight: 600;
          margin: 30px 0 15px 0;
          padding-bottom: 8px;
          border-bottom: 2px solid #D80514;
        }

        p {
          margin-bottom: 15px;
        }

        a {
          color: #D80514;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }



  .intro-image {
    img {
      width: 100%;
      border-radius: 12px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    }
  }
}

// 加载和错误状态样式
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  .loading-spinner,
  .error-content {
    text-align: center;
    color: #909399;

    i {
      font-size: 3rem;
      margin-bottom: 20px;
      color: #D80514;
    }

    h3 {
      margin-bottom: 10px;
      color: #303133;
    }

    p {
      margin-bottom: 20px;
    }
  }
}

.company-culture {
  padding: 66px 0;
  background: #f8f9fa;

  .section-header {
    margin-bottom: 20px;
  }

  .culture-content {
    margin-top: 20px;

    .rich-content {
      color: #666;
      line-height: 1.8;
      font-size: 16px;
      margin: 0 auto;

      img {
        width: 100%;
      }

      h3 {
        color: #D80514;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 30px 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #D80514;
        text-align: center;
      }

      p {
        margin-bottom: 20px;
        text-align: justify;
      }

      a {
        color: #D80514;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .default-culture {
    text-align: center;
    margin-top: 50px;
    color: #999;
    font-style: italic;
  }
}



@media (max-width: 768px) {

  .company-intro,
  .company-culture,
  .innovation,
  .company-timeline,
  .company-stats {
    padding: 50px 0;
  }
}
</style>
